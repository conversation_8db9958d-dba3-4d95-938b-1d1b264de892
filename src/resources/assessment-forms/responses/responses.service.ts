import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { ChartData } from '../dto/chart-data.dto';
import { Assessment } from '../entities/assessment.entity';
import { Response } from '../entities/response.entity';
import type { UpdateResponseDto } from '../dto/updates/update-response.dto';
import type { CreateResponseDto } from '../dto/creates/create-response.dto';
import { Option } from '../entities/option.entity';
import { Question } from '../entities/question.entity';
import { Submission } from '../entities/submission.entity';
import { ItemBlockType } from '../enums/item-block-type.enum';

@Injectable()
export class ResponsesService {
  constructor(
    @InjectEntityManager()
    private entityManager: EntityManager,
    @InjectRepository(Assessment)
    private assessmentRepo: Repository<Assessment>,
    @InjectRepository(Response)
    private responseRepo: Repository<Response>,
  ) {}
  
  async create(createResponseDto: CreateResponseDto): Promise<Response> {
    try {
      // ตรวจสอบว่า submission มีอยู่จริง
      const submission = await this.entityManager.findOne(Submission, {
        where: { id: createResponseDto.submissionId },
        relations: ['assessment']
      });
      
      if (!submission) {
        throw new NotFoundException(
          `Submission with ID ${createResponseDto.submissionId} not found`
        );
      }

      // ตรวจสอบว่า question มีอยู่จริง
      const question = await this.entityManager.findOne(Question, {
        where: { id: createResponseDto.questionId },
        relations: ['itemBlock']
      });

      if (!question) {
        throw new NotFoundException(
          `Question with ID ${createResponseDto.questionId} not found`
        );
      }

      // ตรวจสอบว่าคำถามเป็นประเภทที่ต้องเลือกตัวเลือกหรือไม่
      if ([ItemBlockType.RADIO, ItemBlockType.CHECKBOX].includes(question.itemBlock.type as ItemBlockType) 
          && !createResponseDto.selectedOptionId) {
        throw new BadRequestException('Multiple choice question requires selected option');
      }

      const response = this.responseRepo.create(createResponseDto);
      return await this.responseRepo.save(response);
    } catch (error) {
      throw error;
    }
  }

  async findAll(): Promise<Response[]> {
    try {
      return await this.responseRepo.find({
        relations: ['submission', 'question', 'selectedOption']
      });
    } catch (error) {
      throw error;
    }
  }

  async findOne(id: number): Promise<Response> {
    try {
      const response = await this.responseRepo.findOne({
        where: { id },
        relations: ['submission', 'question', 'selectedOption']
      });

      if (!response) {
        throw new NotFoundException(`Response with ID ${id} not found`);
      }

      return response;
    } catch (error) {
      throw error;
    }
  }

  async update(id: number, updateResponseDto: UpdateResponseDto): Promise<Response> {
    try {
      const response = await this.responseRepo.findOne({
        where: { id },
        relations: ['submission', 'question']
      });

      if (!response) {
        throw new NotFoundException(`Response with ID ${id} not found`);
      }

      // ตรวจสอบว่า submission ยังไม่สิ้นสุด
      if (response.submission.endAt) {
        throw new BadRequestException('Cannot update response after submission is ended');
      }

      await this.responseRepo.update(id, updateResponseDto);
      return await this.responseRepo.findOne({
        where: { id },
        relations: ['submission', 'question', 'selectedOption']
      });
    } catch (error) {
      throw error;
    }
  }

  async remove(id: number): Promise<void> {
    try {
      const response = await this.responseRepo.findOne({
        where: { id },
        relations: ['submission']
      });

      if (!response) {
        throw new NotFoundException(`Response with ID ${id} not found`);
      }

      // ตรวจสอบว่า submission ยังไม่สิ้นสุด
      if (response.submission.endAt) {
        throw new BadRequestException('Cannot delete response after submission is ended');
      }

      await this.responseRepo.delete(id);
    } catch (error) {
      throw error;
    }
  }

  async userSaveQuizResponse(createResponseDto: CreateResponseDto): Promise<Response> {
    try {
      // ตรวจสอบ submission และเวลา
      const submission = await this.entityManager.findOne(Submission, {
        where: { id: createResponseDto.submissionId },
        relations: ['assessment']
      });

      if (!submission) {
        throw new NotFoundException(
          `Submission with ID ${createResponseDto.submissionId} not found`
        );
      }

      // ตรวจสอบว่า submission ยังไม่สิ้นสุด
      if (submission.endAt) {
        throw new BadRequestException('Submission is already ended');
      }

      // ตรวจสอบว่าอยู่ในช่วงเวลาทำแบบทดสอบ
      const now = new Date();
      if (submission.assessment.endAt && now > submission.assessment.endAt) {
        throw new BadRequestException('Assessment period has ended');
      }

      // กรณีคำตอบแบบข้อความ
      if (createResponseDto.answerText) {
        const question = await this.entityManager.findOne(Question, {
          where: { id: createResponseDto.questionId },
          relations: ['itemBlock']
        });

        if (!question) {
          throw new NotFoundException(
            `Question with ID ${createResponseDto.questionId} not found`
          );
        }

        // ตรวจสอบว่าเป็นคำถามแบบข้อความ
        if (![ItemBlockType.TEXTFIELD, ItemBlockType.UPLOAD].includes(question.itemBlock.type as ItemBlockType)) {
          throw new BadRequestException('This question type does not accept text answers');
        }

        // สร้างตัวเลือกใหม่สำหรับคำตอบแบบข้อความ
        const option = this.entityManager.create(Option, {
          itemBlockId: question.itemBlockId,
          optionText: createResponseDto.answerText,
          sequence: 1,
          value: 0,
          nextSection: null
        });
        await this.entityManager.save(Option, option);
        createResponseDto.selectedOptionId = option.id;
      }

      const response = this.entityManager.create(Response, createResponseDto);
      return await this.entityManager.save(Response, response);
    } catch (error) {
      throw error;
    }
  }
}
